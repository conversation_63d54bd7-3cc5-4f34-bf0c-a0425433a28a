"""
Fisher Information Spike Monitor - The 24-Minute Crystallization Detector
=========================================================================

This module implements the Fisher Information spike detection system that identifies
the critical "crystallization point" where market randomness collapses and cascade
events become imminent and deterministic.

Experimental Discovery:
- Fisher Information spike > 1000 signals regime shift from probabilistic to deterministic
- Occurs at ~24-minute mark in key test cases
- Triggers "Red Alert" state requiring immediate cascade execution mode
- Must override standard probabilistic forecasting

Critical Implementation:
- Hard-coded interrupt in main Oracle control loop
- Threshold: F > 1000 = immediate Red Alert
- Action: Switch from probabilistic to deterministic execution focus
"""

import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from scipy.stats import entropy
import warnings
warnings.filterwarnings('ignore', category=RuntimeWarning)

@dataclass
class FisherSpikeResult:
    """Results from Fisher Information spike analysis"""
    fisher_information: float
    spike_detected: bool
    crystallization_strength: float  # 0-1 scale of pattern crystallization
    alert_level: str  # 'normal', 'elevated', 'red_alert'
    time_to_cascade_estimate: Optional[float]  # Minutes until predicted cascade
    confidence: float  # Confidence in spike detection
    regime_state: str  # 'probabilistic' or 'deterministic'
    
class FisherInformationMonitor:
    """
    Fisher Information Spike Monitor - 24-Minute Crystallization Detector
    
    Continuously monitors Fisher Information to detect the critical moment when
    market behavior transitions from random to crystallized, deterministic patterns.
    
    Key Discovery: F > 1000 threshold signals imminent cascade
    Implementation: Hard-coded interrupt in Oracle control loop
    
    Usage:
        monitor = FisherInformationMonitor()
        result = monitor.analyze_spike(rg_scaled_data)
        if result.spike_detected:
            # OVERRIDE probabilistic models - enter deterministic mode
    """
    
    def __init__(self, spike_threshold: float = 1000.0, density_mode: str = "adaptive"):
        """
        Initialize Fisher Information Monitor
        
        Args:
            spike_threshold: Critical threshold for spike detection (default: 1000.0)
            density_mode: Event density handling mode - "adaptive", "high_density", "sparse"
        """
        self.spike_threshold = spike_threshold
        self.density_mode = density_mode
        
        # Adaptive threshold adjustment based on event density
        if density_mode == "high_density":
            # For 15-20 events/hour (micro-event enhanced data)
            self.density_adjustment = 0.2  # Lower thresholds for dense data
            self.spike_threshold = spike_threshold * self.density_adjustment  # 200 for high density
        elif density_mode == "sparse":
            # For 2-3 events/hour (legacy HTF-only data) 
            self.density_adjustment = 1.0  # Keep original thresholds
        else:  # adaptive
            self.density_adjustment = 0.5  # Middle ground - will adjust based on actual density
            self.spike_threshold = spike_threshold * self.density_adjustment  # 500 adaptive
        
        # Alert level thresholds - adjusted for density
        self.alert_thresholds = {
            'normal': self.spike_threshold * 0.1,      # Normal market conditions  
            'elevated': self.spike_threshold * 0.5,    # Elevated tension
            'red_alert': self.spike_threshold          # Imminent cascade
        }
        
        # Enhanced density metrics for micro-event support
        self.density_metrics = {
            'events_per_hour_threshold': 10.0,  # Threshold between sparse and dense
            'last_density_calculation': 0.0,
            'adaptive_threshold_enabled': density_mode == "adaptive"
        }
        
        # Crystallization detection parameters
        self.crystallization_window = 5  # Number of bins for crystallization analysis
        self.min_observations = 3        # Minimum observations for valid calculation
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"🚨 FISHER MONITOR: Crystallization detector initialized")
        self.logger.info(f"   Spike Threshold: {spike_threshold}")
        self.logger.info(f"   Alert Levels: Normal < {self.alert_thresholds['normal']}, "
                        f"Elevated < {self.alert_thresholds['elevated']}, "
                        f"Red Alert ≥ {self.alert_thresholds['red_alert']}")
    
    def calculate_fisher_information(self, event_counts: np.ndarray) -> float:
        """
        Calculate Fisher Information for event count sequence
        
        Enhanced for micro-event dense data streams. Automatically detects
        event density and applies appropriate scaling factors.
        
        Fisher Information measures how much information the data carries about
        an unknown parameter. High Fisher Information indicates the data is
        highly informative about an underlying process - in our case, the
        transition from random to deterministic cascade behavior.
        
        Args:
            event_counts: Array of event counts per time bin (from RG Scaler)
            
        Returns:
            Fisher Information value (density-adjusted)
        """
        if event_counts is None or len(event_counts) < self.min_observations:
            return 0.0
        
        # Calculate event density for adaptive processing
        density = self._calculate_event_density(event_counts)
        
        # Remove zero counts to avoid log(0) issues
        non_zero_counts = event_counts[event_counts > 0]
        if len(non_zero_counts) < 2:
            return 0.0
        
        try:
            # Enhanced Fisher Information calculation with density adaptation
            
            # Method 1: Variance-based Fisher Information (enhanced)
            mean_count = np.mean(non_zero_counts)
            if mean_count <= 0:
                return 0.0
                
            # Normalize counts to probabilities
            total_events = np.sum(non_zero_counts)
            if total_events == 0:
                return 0.0
                
            probabilities = non_zero_counts / total_events
            
            # Calculate Fisher Information as variance of log probabilities
            log_probs = np.log(probabilities + 1e-10)  # Add small epsilon
            fisher_info = np.var(log_probs) * len(probabilities)
            
            # Enhanced scaling with density adaptation
            if density >= self.density_metrics['events_per_hour_threshold']:
                # High-density mode: Enhanced sensitivity for micro-events
                scaling_factor = 50.0  # Lower scaling for dense data
                density_bonus = min(2.0, density / 10.0)  # Bonus for higher density
                scaled_fisher = fisher_info * scaling_factor * density_bonus
            else:
                # Sparse mode: Original scaling
                scaling_factor = 100.0
                scaled_fisher = fisher_info * scaling_factor
            
            # Additional crystallization enhancement for dense data
            if density >= 15.0:  # Target density achieved
                # Look for crystallization patterns in dense data
                crystallization_multiplier = self._detect_dense_crystallization(event_counts)
                scaled_fisher *= crystallization_multiplier
            
            # Store density calculation for adaptive threshold adjustment
            if self.density_metrics['adaptive_threshold_enabled']:
                self._adjust_thresholds_for_density(density)
            
            return float(scaled_fisher)
            
        except (ValueError, ZeroDivisionError, FloatingPointError):
            self.logger.warning("Fisher Information calculation failed - returning 0")
            return 0.0
    
    def _calculate_event_density(self, event_counts: np.ndarray) -> float:
        """Calculate events per hour from event count array"""
        if len(event_counts) == 0:
            return 0.0
        
        # Assume 1-minute bins for high-density data
        total_events = np.sum(event_counts)
        duration_hours = len(event_counts) / 60.0  # Convert minutes to hours
        
        if duration_hours <= 0:
            return 0.0
        
        density = total_events / duration_hours
        self.density_metrics['last_density_calculation'] = density
        return density
    
    def _detect_dense_crystallization(self, event_counts: np.ndarray) -> float:
        """
        Detect crystallization patterns in dense event data
        
        For dense micro-event streams, crystallization appears as:
        - Rapid event clustering (multiple events in consecutive bins)
        - Exponential event acceleration 
        - Sharp variance transitions
        
        Returns:
            Crystallization multiplier (1.0-3.0)
        """
        if len(event_counts) < 10:
            return 1.0
        
        # Look for event clustering (consecutive non-zero bins)
        non_zero_bins = event_counts > 0
        cluster_lengths = []
        current_cluster = 0
        
        for is_event in non_zero_bins:
            if is_event:
                current_cluster += 1
            else:
                if current_cluster > 0:
                    cluster_lengths.append(current_cluster)
                current_cluster = 0
        
        if current_cluster > 0:
            cluster_lengths.append(current_cluster)
        
        # Crystallization indicators
        multiplier = 1.0
        
        # 1. Large event clusters indicate crystallization
        if cluster_lengths:
            max_cluster = max(cluster_lengths)
            if max_cluster >= 5:  # 5+ consecutive minutes of events
                multiplier += 0.5
            if max_cluster >= 10:  # 10+ consecutive minutes
                multiplier += 1.0
        
        # 2. Event acceleration in recent window
        if len(event_counts) >= 20:
            recent_events = event_counts[-10:]  # Last 10 minutes
            early_events = event_counts[-20:-10]  # Previous 10 minutes
            
            recent_sum = np.sum(recent_events)
            early_sum = np.sum(early_events)
            
            if early_sum > 0 and recent_sum > early_sum * 1.5:  # 50% acceleration
                multiplier += 0.8
        
        # 3. Sharp variance transition
        if len(event_counts) >= 15:
            early_variance = np.var(event_counts[:5])
            middle_variance = np.var(event_counts[5:10])
            recent_variance = np.var(event_counts[-5:])
            
            if early_variance > 0 and recent_variance < early_variance * 0.3:  # Variance collapse
                multiplier += 1.2
        
        return min(3.0, multiplier)  # Cap at 3x multiplier
    
    def _adjust_thresholds_for_density(self, density: float):
        """Adaptively adjust thresholds based on measured event density"""
        
        if density >= 15.0:
            # High density detected - use optimized thresholds
            if self.density_mode == "adaptive":
                adjustment = 0.2  # Same as high_density mode
                self.spike_threshold = 1000.0 * adjustment
                self.alert_thresholds = {
                    'normal': self.spike_threshold * 0.1,
                    'elevated': self.spike_threshold * 0.5,
                    'red_alert': self.spike_threshold
                }
        elif density <= 5.0:
            # Low density detected - use traditional thresholds
            if self.density_mode == "adaptive":
                adjustment = 1.0  # Same as sparse mode
                self.spike_threshold = 1000.0 * adjustment
                self.alert_thresholds = {
                    'normal': self.spike_threshold * 0.1,
                    'elevated': self.spike_threshold * 0.5, 
                    'red_alert': self.spike_threshold
                }
        # else: keep current adaptive thresholds
    
    def detect_crystallization_pattern(self, event_counts: np.ndarray) -> Tuple[float, str]:
        """
        Detect crystallization patterns indicating transition to deterministic behavior
        
        Args:
            event_counts: RG-scaled event counts
            
        Returns:
            Tuple of (crystallization_strength, pattern_description)
        """
        if len(event_counts) < self.crystallization_window:
            return 0.0, "insufficient_data"
        
        try:
            # Analyze recent crystallization window
            recent_counts = event_counts[-self.crystallization_window:]
            
            # Calculate multiple crystallization indicators
            indicators = {}
            
            # 1. Variance collapse (randomness reduction)
            if len(event_counts) >= 10:
                early_variance = np.var(event_counts[:5])
                recent_variance = np.var(recent_counts)
                
                if early_variance > 0:
                    variance_ratio = recent_variance / early_variance
                    indicators['variance_collapse'] = 1.0 - variance_ratio
                else:
                    indicators['variance_collapse'] = 0.0
            else:
                indicators['variance_collapse'] = 0.0
            
            # 2. Pattern acceleration (increasing event density)
            if len(recent_counts) >= 3:
                differences = np.diff(recent_counts)
                if len(differences) >= 2:
                    acceleration = np.mean(differences[-2:]) - np.mean(differences[:2])
                    indicators['acceleration'] = min(1.0, max(0.0, acceleration / 10.0))
                else:
                    indicators['acceleration'] = 0.0
            else:
                indicators['acceleration'] = 0.0
            
            # 3. Entropy reduction (pattern crystallization)
            if np.sum(recent_counts) > 0:
                recent_probs = recent_counts / np.sum(recent_counts)
                recent_entropy = entropy(recent_probs + 1e-10)
                max_entropy = np.log(len(recent_counts))
                
                if max_entropy > 0:
                    entropy_reduction = 1.0 - (recent_entropy / max_entropy)
                    indicators['entropy_reduction'] = entropy_reduction
                else:
                    indicators['entropy_reduction'] = 0.0
            else:
                indicators['entropy_reduction'] = 0.0
            
            # Combine indicators for overall crystallization strength
            weights = {
                'variance_collapse': 0.4,
                'acceleration': 0.3,
                'entropy_reduction': 0.3
            }
            
            crystallization_strength = sum(
                indicators[key] * weights[key] for key in indicators
            )
            crystallization_strength = max(0.0, min(1.0, crystallization_strength))
            
            # Determine pattern description
            if crystallization_strength > 0.8:
                pattern = "strong_crystallization"
            elif crystallization_strength > 0.5:
                pattern = "moderate_crystallization"
            elif crystallization_strength > 0.2:
                pattern = "weak_crystallization"
            else:
                pattern = "random_behavior"
            
            return crystallization_strength, pattern
            
        except Exception as e:
            self.logger.warning(f"Crystallization detection failed: {e}")
            return 0.0, "analysis_error"
    
    def estimate_cascade_timing(self, fisher_info: float, crystallization_strength: float) -> Optional[float]:
        """
        Estimate time until cascade based on Fisher Information and crystallization
        
        Args:
            fisher_info: Current Fisher Information level
            crystallization_strength: Crystallization pattern strength (0-1)
            
        Returns:
            Estimated minutes until cascade, or None if insufficient signal
        """
        if fisher_info < self.alert_thresholds['elevated']:
            return None
        
        try:
            # Base timing estimate on Fisher Information level
            if fisher_info >= self.spike_threshold:
                # RED ALERT: Immediate cascade expected
                base_estimate = 0.5  # 30 seconds
            elif fisher_info >= self.alert_thresholds['elevated']:
                # Elevated alert: Linear interpolation
                progress = (fisher_info - self.alert_thresholds['elevated']) / (self.spike_threshold - self.alert_thresholds['elevated'])
                base_estimate = 10.0 * (1.0 - progress)  # 10 minutes down to 0.5 minutes
            else:
                return None
            
            # Adjust based on crystallization strength
            crystallization_adjustment = 1.0 - (crystallization_strength * 0.5)
            adjusted_estimate = base_estimate * crystallization_adjustment
            
            return max(0.1, adjusted_estimate)  # Minimum 6 seconds
            
        except Exception as e:
            self.logger.warning(f"Cascade timing estimation failed: {e}")
            return None
    
    def analyze_spike(self, rg_scaled_data: np.ndarray) -> FisherSpikeResult:
        """
        Comprehensive Fisher Information spike analysis
        
        This is the main analysis method that should be called by the Oracle
        control loop to monitor for crystallization events.
        
        Args:
            rg_scaled_data: RG-scaled event counts from production RG Scaler
            
        Returns:
            FisherSpikeResult with comprehensive spike analysis
        """
        if rg_scaled_data is None or len(rg_scaled_data) < self.min_observations:
            return FisherSpikeResult(
                fisher_information=0.0,
                spike_detected=False,
                crystallization_strength=0.0,
                alert_level='normal',
                time_to_cascade_estimate=None,
                confidence=0.0,
                regime_state='probabilistic'
            )
        
        # Step 1: Calculate Fisher Information
        fisher_info = self.calculate_fisher_information(rg_scaled_data)
        
        # Step 2: Detect crystallization patterns
        crystallization_strength, crystallization_pattern = self.detect_crystallization_pattern(rg_scaled_data)
        
        # Step 3: Determine alert level
        if fisher_info >= self.alert_thresholds['red_alert']:
            alert_level = 'red_alert'
            regime_state = 'deterministic'
            spike_detected = True
        elif fisher_info >= self.alert_thresholds['elevated']:
            alert_level = 'elevated'
            regime_state = 'probabilistic'
            spike_detected = False
        else:
            alert_level = 'normal'
            regime_state = 'probabilistic'
            spike_detected = False
        
        # Step 4: Estimate cascade timing
        cascade_estimate = self.estimate_cascade_timing(fisher_info, crystallization_strength)
        
        # Step 5: Calculate confidence in spike detection
        confidence_factors = [
            fisher_info / self.spike_threshold if fisher_info > 0 else 0,
            crystallization_strength,
            len(rg_scaled_data) / 10.0  # Data sufficiency factor
        ]
        confidence = min(1.0, np.mean(confidence_factors))
        
        result = FisherSpikeResult(
            fisher_information=fisher_info,
            spike_detected=spike_detected,
            crystallization_strength=crystallization_strength,
            alert_level=alert_level,
            time_to_cascade_estimate=cascade_estimate,
            confidence=confidence,
            regime_state=regime_state
        )
        
        # Log critical alerts
        if alert_level == 'red_alert':
            self.logger.critical(f"🚨 RED ALERT: Fisher spike detected! F={fisher_info:.1f}")
            self.logger.critical(f"   Crystallization: {crystallization_strength:.3f}")
            self.logger.critical(f"   Cascade ETA: {cascade_estimate:.1f} minutes")
            self.logger.critical(f"   REGIME: DETERMINISTIC - Override probabilistic models!")
        elif alert_level == 'elevated':
            self.logger.warning(f"⚠️ ELEVATED: Fisher tension rising F={fisher_info:.1f}")
            
        return result
    
    def get_monitor_status(self) -> Dict[str, Any]:
        """
        Get current monitor configuration and status
        
        Returns:
            Dictionary with monitor parameters and thresholds
        """
        return {
            'spike_threshold': self.spike_threshold,
            'alert_thresholds': self.alert_thresholds,
            'crystallization_window': self.crystallization_window,
            'min_observations': self.min_observations,
            'monitor_type': 'fisher_information_crystallization_detector',
            'experimental_basis': '24-minute crystallization phenomenon',
            'critical_discovery': 'F > 1000 signals probabilistic → deterministic transition'
        }


# Production factory functions
def create_fisher_monitor(spike_threshold: float = 1000.0, density_mode: str = "adaptive") -> FisherInformationMonitor:
    """
    Create production-ready Fisher Information Monitor
    
    Args:
        spike_threshold: Critical Fisher Information threshold for Red Alert
        density_mode: Event density handling mode - "adaptive", "high_density", "sparse"
        
    Returns:
        Configured FisherInformationMonitor instance
    """
    return FisherInformationMonitor(spike_threshold=spike_threshold, density_mode=density_mode)

def create_high_density_fisher_monitor() -> FisherInformationMonitor:
    """
    Create Fisher monitor optimized for micro-event dense data (15-20 events/hour)
    
    This factory creates a monitor specifically calibrated for the enhanced
    micro-event extraction pipeline with lower thresholds and enhanced
    crystallization detection.
    
    Returns:
        High-density optimized FisherInformationMonitor
    """
    return FisherInformationMonitor(spike_threshold=1000.0, density_mode="high_density")


if __name__ == "__main__":
    """
    Test and demonstrate the Fisher Information Monitor
    """
    print("🚨 FISHER INFORMATION MONITOR: Crystallization Detection Testing")
    print("=" * 70)
    
    # Create Fisher monitor
    fisher_monitor = create_fisher_monitor(spike_threshold=1000.0)
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'Normal Market Conditions',
            'data': np.array([2, 1, 3, 2, 1, 2, 3, 1, 2])  # Random distribution
        },
        {
            'name': 'Building Tension',
            'data': np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])  # Accelerating pattern
        },
        {
            'name': 'Crystallization Event',
            'data': np.array([1, 1, 2, 5, 15, 25, 40, 60, 80, 100])  # Exponential acceleration
        },
        {
            'name': 'Extreme Spike (Red Alert)',
            'data': np.array([1, 2, 5, 20, 50, 100, 200, 500, 800, 1000])  # Massive acceleration
        }
    ]
    
    print("\\n🔍 TESTING FISHER SPIKE DETECTION:")
    print("-" * 60)
    
    for scenario in test_scenarios:
        result = fisher_monitor.analyze_spike(scenario['data'])
        
        print(f"\\n📊 {scenario['name']}:")
        print(f"   Fisher Information: {result.fisher_information:.1f}")
        print(f"   Alert Level: {result.alert_level.upper()}")
        print(f"   Spike Detected: {'🚨 YES' if result.spike_detected else '✅ No'}")
        print(f"   Crystallization: {result.crystallization_strength:.3f}")
        print(f"   Regime State: {result.regime_state.upper()}")
        print(f"   Confidence: {result.confidence:.3f}")
        
        if result.time_to_cascade_estimate:
            print(f"   Cascade ETA: {result.time_to_cascade_estimate:.1f} minutes")
        
        if result.spike_detected:
            print(f"   🚨 ACTION REQUIRED: Override probabilistic models!")
    
    # Show monitor configuration
    print(f"\\n⚙️ FISHER MONITOR CONFIGURATION:")
    config = fisher_monitor.get_monitor_status()
    print(f"   Spike Threshold: {config['spike_threshold']}")
    print(f"   Red Alert: F ≥ {config['alert_thresholds']['red_alert']}")
    print(f"   Elevated Alert: F ≥ {config['alert_thresholds']['elevated']}")
    print(f"   Experimental Basis: {config['experimental_basis']}")
    
    print(f"\\n✅ FISHER MONITOR: Testing complete")
    print(f"🔗 Ready for integration as Red Alert interrupt system")