{"permissions": {"allow": ["Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(python3:*)", "Bash(find:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(time python3:*)", "Bash(export PYTHONUNBUFFERED=1)", "Bash(pip install:*)", "Bash(grep:*)", "Bash(system_profiler:*)", "Bash(lpstat:*)", "Bash(ls:*)", "<PERSON><PERSON>(lpadmin:*)", "<PERSON><PERSON>(nvim:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(echo)", "Bash(tree:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "Bash(git push:*)", "<PERSON><PERSON>(touch:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(env)", "Bash(export:*)", "Bash(export OMP_NUM_THREADS=4)", "Bash(./run_with_xgboost.sh:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(true)"], "deny": []}}