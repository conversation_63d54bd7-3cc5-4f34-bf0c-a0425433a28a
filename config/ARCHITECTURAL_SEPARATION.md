# ARCHITECTURAL SEPARATION DOCUMENTATION

## Overview
This document details the clear architectural separation implemented to prevent AI confusion and enable parallel development of the system's different theoretical models.

## System Architecture

The system is composed of several distinct components that can be run independently or together.

### Core Mathematical Pipeline (LOCAL - NO GROK)
This is the foundational layer that processes raw session data into meaningful metrics. The A->B->C->D pipeline is the core of this layer.

```
Session Data → Local Unit A → Local Unit B → Local Unit C → Local Unit D → Results
             (NumPy/SciPy)   (Mathematical)  (Temporal)    (Integration)
```

**Files:**
- `src/local_units.py` - Complete A→B→C→D mathematical pipeline

**Crucially, the output of this pipeline, especially the energy metrics from Unit B, is the primary input for the Energy Paradigm model.**

### The Energy Paradigm (LOCAL - NO GROK)
This is the primary predictive engine of the system. It is a pure mathematical model with no external dependencies.

```
Pipeline Results → Energy Paradigm Model → Cascade Prediction
```

**Files:**
- `src/energy_paradigm_production.py`
- `src/energy_integration_interface.py`

**Dependencies:** NumPy, SciPy only - NO GROK API

### Supporting Models (LOCAL - NO GROK)
These models provide additional context and analysis. They are also pure mathematical models.

-   **Hawkes Process Model:** Analyzes background state and contagion risk.
    -   `src/hawkes_cascade_predictor.py`
-   **RG Analysis Model:** Analyzes multi-scale structural stability.
    -   `rg_cascade_predictor.py`

### Phase 4: Grok Narrative Analysis (SEPARATED)
This component is completely separate and is used for experimental narrative and theoretical work.

```
Session Data + Mathematical Results → Grok 4 API → Narrative Analysis
                                   (ISOLATED)     (Theoretical Only)
```

**Files:**
- `src/grok_narrative_analyzer.py` - Grok 4 narrative analysis (separated)

**Purpose:**
-   **NO MATHEMATICAL CALCULATIONS**
-   Narrative interpretation
-   Theoretical insights
-   Experimental features

## Separation Benefits

### 1. Troubleshooting Clarity
-   **Energy Paradigm:** Pure mathematical, no API dependencies.
-   **Supporting Models:** Pure mathematical, no API dependencies.
-   **Narrative System:** Clearly isolated, obvious when active.

### 2. Future Development
-   The different mathematical models can be developed and tested in parallel.
-   Experimental work with the Grok API does not affect the core mathematical models.

## Usage Patterns

### Production (Recommended)
```python
# Run the core pipeline and the Energy Paradigm model.
# Optionally, run the Hawkes and RG models for additional context.
```

### Experimental/Theoretical Work
```python  
# Enable Grok narrative analysis for research, while using the
# core mathematical models for baseline data.
```

This clear separation ensures that the system is robust, reliable, and easy to maintain, while still allowing for cutting-edge experimental research.
