#!/usr/bin/env python3
"""
Level-1 JSON Standardization Engine
Converts all schema variations to unified target format with zero data loss.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
import re
import shutil
from copy import deepcopy

class Lvl1StandardizationEngine:
    """Convert all Lvl-1 files to standardized target schema."""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.standardization_stats = {
            "files_processed": 0,
            "current_schema_preserved": 0,
            "legacy_schema_converted": 0,
            "partial_schema_completed": 0,
            "corrupted_skipped": 0,
            "conversion_errors": 0
        }
        
        # Target schema template
        self.target_schema_template = {
            "session_metadata": {
                "session_type": "",
                "session_date": "",
                "session_start": "",
                "session_end": "",
                "session_duration": 0,
                "transcription_source": "live_market_data",
                "data_completeness": "complete_session",
                "timezone": "ET",
                "session_status": "completed"
            },
            "session_fpfvg": {
                "fpfvg_present": False,
                "fpfvg_formation": {
                    "formation_time": "00:00:00",
                    "premium_high": 0.0,
                    "discount_low": 0.0,
                    "gap_size": 0.0,
                    "interactions": []
                }
            },
            "price_movements": [],
            "session_liquidity_events": [],
            "energy_state": {
                "energy_density": 0.5,
                "total_accumulated": 0.0,
                "energy_rate": 30.0,
                "energy_source": "standardization_estimation",
                "session_duration": 0,
                "expansion_phases": 0,
                "retracement_phases": 0,
                "consolidation_phases": 0,
                "phase_transitions": 0,
                "session_status": "complete"
            },
            "contamination_analysis": {
                "htf_contamination": {
                    "immediate_cross_session_interaction": False,
                    "htf_carryover_strength": 0.3,
                    "cross_session_inheritance": 0.3
                },
                "cross_session_inheritance": {
                    "energy_carryover_coefficient": 0.3
                }
            }
        }
        
        # Field mappings for legacy conversion
        self.field_mappings = {
            "date": "session_date",
            "start_time": "session_start",
            "end_time": "session_end", 
            "duration_minutes": "session_duration"
        }
        
        # Session type normalization
        self.session_type_mappings = {
            "Asia": "asia",
            "London": "london", 
            "Lunch": "lunch",
            "Pre-Market": "premarket",
            "NY AM": "nyam",
            "NY PM": "nypm",
            "Midnight": "midnight",
            "ASIA": "asia",
            "LONDON": "london",
            "LUNCH": "lunch",
            "PREMARKET": "premarket",
            "NYAM": "nyam",
            "NYPM": "nypm",
            "MIDNIGHT": "midnight",
            "PM": "nypm",
            "PREASIA": "premarket"
        }

    def standardize_file(self, file_path: Path) -> Dict[str, Any]:
        """Standardize individual file to target schema."""
        try:
            with open(file_path, 'r') as f:
                original_data = json.load(f)
            
            # Determine schema type and apply appropriate conversion
            if "level1_json" in original_data:
                # Current schema - preserve as-is but validate
                standardized = self._validate_current_schema(original_data["level1_json"])
                conversion_type = "current_schema_preserved"
            elif "price_data" in original_data:
                # Legacy schema - convert
                standardized = self._convert_legacy_schema(original_data)
                conversion_type = "legacy_schema_converted"
            else:
                # Partial schema - complete missing fields
                standardized = self._complete_partial_schema(original_data)
                conversion_type = "partial_schema_completed"
            
            # Add processing metadata
            standardized["processing_metadata"] = {
                "original_file": str(file_path),
                "standardization_date": datetime.now().isoformat(),
                "conversion_type": conversion_type,
                "schema_version": "target_v1.0"
            }
            
            return {
                "success": True,
                "standardized_data": standardized,
                "conversion_type": conversion_type,
                "original_file": str(file_path)
            }
            
        except json.JSONDecodeError as e:
            return {
                "success": False,
                "error": f"JSON decode error: {str(e)}",
                "error_type": "corrupted",
                "original_file": str(file_path)
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Conversion error: {str(e)}",
                "error_type": "conversion_error",
                "original_file": str(file_path)
            }

    def _validate_current_schema(self, data: Dict) -> Dict:
        """Validate and preserve current schema files."""
        # Current schema files are already compliant, just ensure completeness
        standardized = deepcopy(self.target_schema_template)
        
        # Merge existing data, preserving all original information
        for key, value in data.items():
            if key in standardized:
                if isinstance(value, dict) and isinstance(standardized[key], dict):
                    standardized[key].update(value)
                else:
                    standardized[key] = value
            else:
                # Preserve any additional fields not in template
                standardized[key] = value
        
        return standardized

    def _convert_legacy_schema(self, data: Dict) -> Dict:
        """Convert legacy schema to target format."""
        standardized = deepcopy(self.target_schema_template)
        
        # Convert session_metadata
        if "session_metadata" in data:
            session_meta = data["session_metadata"]
            target_meta = standardized["session_metadata"]
            
            # Map legacy fields
            for legacy_field, target_field in self.field_mappings.items():
                if legacy_field in session_meta:
                    target_meta[target_field] = session_meta[legacy_field]
            
            # Normalize session type
            session_type = session_meta.get("session_type", "")
            if session_type in self.session_type_mappings:
                target_meta["session_type"] = self.session_type_mappings[session_type]
            else:
                target_meta["session_type"] = session_type.lower()
            
            # Clean up time fields (remove " ET" suffix if present)
            for time_field in ["session_start", "session_end"]:
                if time_field in target_meta and isinstance(target_meta[time_field], str):
                    target_meta[time_field] = target_meta[time_field].replace(" ET", "")
        
        # Convert price_data to price_movements
        if "price_data" in data:
            price_data = data["price_data"]
            movements = []
            
            # Add OHLC as price movements
            if "open" in price_data:
                movements.append({
                    "timestamp": standardized["session_metadata"]["session_start"],
                    "price_level": price_data["open"],
                    "movement_type": "open"
                })
            
            if "high" in price_data:
                movements.append({
                    "timestamp": "12:00:00",  # Approximate mid-session
                    "price_level": price_data["high"],
                    "movement_type": "session_high"
                })
            
            if "low" in price_data:
                movements.append({
                    "timestamp": "12:30:00",  # Approximate mid-session
                    "price_level": price_data["low"],
                    "movement_type": "session_low"
                })
            
            if "close" in price_data:
                movements.append({
                    "timestamp": standardized["session_metadata"]["session_end"],
                    "price_level": price_data["close"],
                    "movement_type": "close"
                })
            
            standardized["price_movements"] = movements
        
        # Preserve existing price_movements if present
        if "price_movements" in data:
            standardized["price_movements"].extend(data["price_movements"])
        
        # Calculate energy_state from available data
        duration = standardized["session_metadata"]["session_duration"]
        if duration > 0:
            standardized["energy_state"]["session_duration"] = duration
            standardized["energy_state"]["total_accumulated"] = duration * 0.5
            standardized["energy_state"]["expansion_phases"] = max(1, len(standardized["price_movements"]) // 3)
            standardized["energy_state"]["retracement_phases"] = max(1, len(standardized["price_movements"]) // 4)
            standardized["energy_state"]["consolidation_phases"] = max(1, len(standardized["price_movements"]) // 5)
            standardized["energy_state"]["phase_transitions"] = max(0, len(standardized["price_movements"]) - 1)
        
        # Preserve any additional fields
        for key, value in data.items():
            if key not in ["session_metadata", "price_data", "price_movements"] and key not in standardized:
                standardized[key] = value
        
        return standardized

    def _complete_partial_schema(self, data: Dict) -> Dict:
        """Complete partial schema with missing required fields."""
        standardized = deepcopy(self.target_schema_template)
        
        # Merge existing data
        for key, value in data.items():
            if key in standardized:
                if isinstance(value, dict) and isinstance(standardized[key], dict):
                    standardized[key].update(value)
                else:
                    standardized[key] = value
            else:
                standardized[key] = value
        
        return standardized

    def standardize_all_files(self, file_list: List[Path]) -> Dict[str, Any]:
        """Standardize all files in the provided list."""
        print(f"\n🔧 Starting standardization of {len(file_list)} files...")
        
        results = {
            "successful_conversions": [],
            "failed_conversions": [],
            "conversion_summary": {}
        }
        
        for file_path in file_list:
            print(f"   Standardizing: {file_path.name}")
            result = self.standardize_file(file_path)
            
            if result["success"]:
                results["successful_conversions"].append(result)
                conversion_type = result["conversion_type"]
                if conversion_type not in results["conversion_summary"]:
                    results["conversion_summary"][conversion_type] = 0
                results["conversion_summary"][conversion_type] += 1
                
                # Update stats
                if conversion_type == "current_schema_preserved":
                    self.standardization_stats["current_schema_preserved"] += 1
                elif conversion_type == "legacy_schema_converted":
                    self.standardization_stats["legacy_schema_converted"] += 1
                elif conversion_type == "partial_schema_completed":
                    self.standardization_stats["partial_schema_completed"] += 1
            else:
                results["failed_conversions"].append(result)
                if result["error_type"] == "corrupted":
                    self.standardization_stats["corrupted_skipped"] += 1
                else:
                    self.standardization_stats["conversion_errors"] += 1
            
            self.standardization_stats["files_processed"] += 1
        
        return results

if __name__ == "__main__":
    print("🚀 Level-1 JSON Standardization Engine")
    print("=" * 50)
    
    # This will be called by the main migration script
    pass
