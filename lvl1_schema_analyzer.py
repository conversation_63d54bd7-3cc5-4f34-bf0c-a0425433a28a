#!/usr/bin/env python3
"""
Level-1 JSON Schema Analyzer
Comprehensive analysis and categorization of all Lvl-1 files for standardization.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import re

class Lvl1SchemaAnalyzer:
    """Analyze and categorize all Lvl-1 JSON files by schema type."""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.analysis_results = {
            "total_files": 0,
            "current_schema": [],
            "legacy_schema": [],
            "partial_schema": [],
            "corrupted": [],
            "schema_variations": {},
            "date_coverage": {},
            "session_type_coverage": {}
        }
        
        # Define target schema requirements
        self.target_schema_fields = {
            "session_metadata": ["session_type", "session_date", "session_start", "session_end", "session_duration"],
            "session_fpfvg": ["fpfvg_present", "fpfvg_formation"],
            "price_movements": [],
            "session_liquidity_events": [],
            "energy_state": ["energy_density", "total_accumulated", "energy_rate"],
            "contamination_analysis": ["htf_contamination", "cross_session_inheritance"]
        }
        
        # Legacy field mappings
        self.legacy_mappings = {
            "date": "session_date",
            "start_time": "session_start", 
            "end_time": "session_end",
            "duration_minutes": "session_duration",
            "Asia": "asia",
            "London": "london",
            "Lunch": "lunch",
            "Pre-Market": "premarket",
            "NY AM": "nyam",
            "NY PM": "nypm",
            "Midnight": "midnight"
        }

    def discover_all_files(self) -> List[Path]:
        """Find all Lvl-1 JSON files recursively."""
        print("🔍 Discovering all Lvl-1 JSON files...")
        
        lvl1_files = []
        for pattern in ["*Lvl-1*.json", "*lvl-1*.json", "*LVL-1*.json"]:
            lvl1_files.extend(self.base_path.rglob(pattern))
        
        # Filter out enhanced files
        lvl1_files = [f for f in lvl1_files if "enhanced" not in str(f).lower()]
        
        print(f"📊 Found {len(lvl1_files)} Lvl-1 files")
        return sorted(lvl1_files)

    def analyze_file_schema(self, file_path: Path) -> Dict[str, Any]:
        """Analyze individual file schema and categorize."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            analysis = {
                "file_path": str(file_path),
                "file_name": file_path.name,
                "schema_type": "unknown",
                "has_level1_wrapper": "level1_json" in data,
                "top_level_keys": list(data.keys()),
                "missing_required": [],
                "legacy_fields": [],
                "session_type": None,
                "session_date": None,
                "file_size": file_path.stat().st_size,
                "valid_json": True
            }
            
            # Check for level1_json wrapper
            working_data = data.get("level1_json", data)
            
            # Extract session info
            session_meta = working_data.get("session_metadata", {})
            analysis["session_type"] = session_meta.get("session_type") or session_meta.get("session_name", "").split("_")[0].lower()
            analysis["session_date"] = session_meta.get("session_date") or session_meta.get("date")
            
            # Check schema compliance
            analysis["schema_type"] = self._categorize_schema(working_data, analysis)
            
            return analysis
            
        except json.JSONDecodeError as e:
            return {
                "file_path": str(file_path),
                "file_name": file_path.name,
                "schema_type": "corrupted",
                "error": str(e),
                "valid_json": False
            }
        except Exception as e:
            return {
                "file_path": str(file_path),
                "file_name": file_path.name,
                "schema_type": "error",
                "error": str(e),
                "valid_json": False
            }

    def _categorize_schema(self, data: Dict, analysis: Dict) -> str:
        """Categorize schema type based on structure."""
        
        # Check for current schema (has all required top-level keys)
        required_keys = ["session_metadata", "session_fpfvg", "session_liquidity_events"]
        has_required = all(key in data for key in required_keys)
        
        if has_required:
            # Check session_metadata structure
            session_meta = data.get("session_metadata", {})
            has_new_fields = all(field in session_meta for field in ["session_date", "session_start", "session_end"])
            
            if has_new_fields:
                return "current_schema"
            else:
                analysis["legacy_fields"].extend([k for k in ["date", "start_time", "end_time"] if k in session_meta])
                return "legacy_schema"
        
        # Check for legacy schema (has price_data)
        if "price_data" in data:
            analysis["legacy_fields"].append("price_data")
            return "legacy_schema"
        
        # Check for partial schema
        has_some_required = any(key in data for key in required_keys)
        if has_some_required:
            analysis["missing_required"] = [key for key in required_keys if key not in data]
            return "partial_schema"
        
        return "unknown_schema"

    def analyze_all_files(self) -> Dict[str, Any]:
        """Analyze all discovered files."""
        print("\n🔬 Analyzing schema variations...")
        
        files = self.discover_all_files()
        self.analysis_results["total_files"] = len(files)
        
        for file_path in files:
            print(f"   Analyzing: {file_path.name}")
            analysis = self.analyze_file_schema(file_path)
            
            # Categorize by schema type
            schema_type = analysis["schema_type"]
            if schema_type not in self.analysis_results:
                self.analysis_results[schema_type] = []
            self.analysis_results[schema_type].append(analysis)
            
            # Track coverage
            if analysis.get("session_date"):
                date = analysis["session_date"]
                if date not in self.analysis_results["date_coverage"]:
                    self.analysis_results["date_coverage"][date] = []
                self.analysis_results["date_coverage"][date].append(analysis["file_name"])
            
            if analysis.get("session_type"):
                session_type = analysis["session_type"].lower()
                if session_type not in self.analysis_results["session_type_coverage"]:
                    self.analysis_results["session_type_coverage"][session_type] = 0
                self.analysis_results["session_type_coverage"][session_type] += 1
        
        return self.analysis_results

    def generate_analysis_report(self) -> str:
        """Generate comprehensive analysis report."""
        results = self.analysis_results
        
        report = f"""
# Level-1 JSON Schema Analysis Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 DISCOVERY SUMMARY
- **Total Files Found**: {results['total_files']}
- **Current Schema**: {len(results.get('current_schema', []))} files
- **Legacy Schema**: {len(results.get('legacy_schema', []))} files  
- **Partial Schema**: {len(results.get('partial_schema', []))} files
- **Corrupted/Error**: {len(results.get('corrupted', [])) + len(results.get('error', []))} files

## 📅 DATE COVERAGE
"""
        
        for date, files in sorted(results["date_coverage"].items()):
            report += f"- **{date}**: {len(files)} files\n"
        
        report += f"\n## 🎯 SESSION TYPE COVERAGE\n"
        for session_type, count in sorted(results["session_type_coverage"].items()):
            report += f"- **{session_type.upper()}**: {count} files\n"
        
        report += f"\n## 🔍 SCHEMA BREAKDOWN\n"
        
        # Current schema files
        if results.get('current_schema'):
            report += f"\n### ✅ CURRENT SCHEMA ({len(results['current_schema'])} files)\n"
            for file_info in results['current_schema']:
                report += f"- {file_info['file_name']}\n"
        
        # Legacy schema files  
        if results.get('legacy_schema'):
            report += f"\n### 🔄 LEGACY SCHEMA ({len(results['legacy_schema'])} files)\n"
            for file_info in results['legacy_schema']:
                legacy_fields = ", ".join(file_info.get('legacy_fields', []))
                report += f"- {file_info['file_name']} (legacy: {legacy_fields})\n"
        
        # Partial schema files
        if results.get('partial_schema'):
            report += f"\n### ⚠️ PARTIAL SCHEMA ({len(results['partial_schema'])} files)\n"
            for file_info in results['partial_schema']:
                missing = ", ".join(file_info.get('missing_required', []))
                report += f"- {file_info['file_name']} (missing: {missing})\n"
        
        # Corrupted files
        corrupted_files = results.get('corrupted', []) + results.get('error', [])
        if corrupted_files:
            report += f"\n### ❌ CORRUPTED/ERROR ({len(corrupted_files)} files)\n"
            for file_info in corrupted_files:
                error = file_info.get('error', 'Unknown error')
                report += f"- {file_info['file_name']}: {error}\n"
        
        return report

    def save_analysis_report(self, output_path: str = "lvl1_analysis_report.md"):
        """Save analysis report to file."""
        report = self.generate_analysis_report()
        
        with open(output_path, 'w') as f:
            f.write(report)
        
        print(f"📝 Analysis report saved to: {output_path}")
        return output_path

if __name__ == "__main__":
    print("🚀 Level-1 JSON Schema Analyzer")
    print("=" * 50)
    
    analyzer = Lvl1SchemaAnalyzer("/Users/<USER>/grok-claude-automation")
    results = analyzer.analyze_all_files()
    
    # Print summary
    print(f"\n📊 ANALYSIS COMPLETE")
    print(f"Total files: {results['total_files']}")
    print(f"Current schema: {len(results.get('current_schema', []))}")
    print(f"Legacy schema: {len(results.get('legacy_schema', []))}")
    print(f"Partial schema: {len(results.get('partial_schema', []))}")
    print(f"Corrupted: {len(results.get('corrupted', [])) + len(results.get('error', []))}")
    
    # Save detailed report
    report_path = analyzer.save_analysis_report()
    print(f"\n✅ Detailed analysis saved to: {report_path}")
