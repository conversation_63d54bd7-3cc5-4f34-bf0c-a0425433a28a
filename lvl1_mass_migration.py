#!/usr/bin/env python3
"""
Level-1 Mass Migration Orchestrator
Complete standardization and migration of all Lvl-1 files to project_oracle structure.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import shutil
from collections import defaultdict

# Import our custom modules
from lvl1_schema_analyzer import Lvl1SchemaAnalyzer
from lvl1_standardization_engine import Lvl1StandardizationEngine

class Lvl1MassMigration:
    """Orchestrate complete migration of all Lvl-1 files."""
    
    def __init__(self, base_path: str = "/Users/<USER>/grok-claude-automation"):
        self.base_path = Path(base_path)
        self.target_dir = self.base_path / "project_oracle" / "data" / "sessions" / "level_1"
        self.backup_dir = self.base_path / "project_oracle" / "backup" / "original_files"
        
        # Initialize components
        self.analyzer = Lvl1SchemaAnalyzer(base_path)
        self.standardizer = Lvl1StandardizationEngine(base_path)
        
        # Migration statistics
        self.migration_stats = {
            "total_files_discovered": 0,
            "successfully_migrated": 0,
            "failed_migrations": 0,
            "duplicates_resolved": 0,
            "files_by_date": defaultdict(list),
            "files_by_session_type": defaultdict(int),
            "quality_issues": []
        }

    def create_directory_structure(self):
        """Create organized directory structure for migration."""
        print("📁 Creating project_oracle directory structure...")
        
        # Create main directories
        directories = [
            self.target_dir,
            self.target_dir / "2025_07",
            self.target_dir / "2025_08", 
            self.backup_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"   Created: {directory}")

    def generate_standard_filename(self, session_type: str, session_date: str) -> str:
        """Generate standardized filename."""
        # Normalize session type
        session_type = session_type.upper()
        
        # Handle date format variations
        if "_" in session_date:
            # Convert 2025_07_28 to 2025-07-28
            session_date = session_date.replace("_", "-")
        
        # Convert date to filename format
        date_parts = session_date.split("-")
        if len(date_parts) == 3:
            year, month, day = date_parts
            filename_date = f"{year}_{month}_{day}"
        else:
            filename_date = session_date.replace("-", "_")
        
        return f"{session_type}_Lvl-1_{filename_date}.json"

    def resolve_duplicates(self, files_by_key: Dict[str, List[Dict]]) -> Dict[str, Dict]:
        """Resolve duplicate files using priority system."""
        print("🔍 Resolving duplicate files...")
        
        resolved_files = {}
        priority_order = ["COMPLETE", "CORRECTED", "REAL", "NEW", "base"]
        
        for key, file_list in files_by_key.items():
            if len(file_list) == 1:
                resolved_files[key] = file_list[0]
            else:
                print(f"   Resolving duplicates for {key}: {len(file_list)} files")
                
                # Sort by priority
                def get_priority(file_info):
                    filename = file_info["file_name"]
                    for i, priority in enumerate(priority_order):
                        if priority in filename:
                            return i
                    return len(priority_order)  # Base files get lowest priority
                
                sorted_files = sorted(file_list, key=get_priority)
                chosen_file = sorted_files[0]
                resolved_files[key] = chosen_file
                
                print(f"     Chose: {chosen_file['file_name']}")
                self.migration_stats["duplicates_resolved"] += len(file_list) - 1
        
        return resolved_files

    def migrate_file(self, file_info: Dict, standardized_data: Dict) -> bool:
        """Migrate individual file to target location."""
        try:
            # Extract metadata
            session_meta = standardized_data.get("session_metadata", {})
            session_type = session_meta.get("session_type", "unknown")
            session_date = session_meta.get("session_date", "unknown")
            
            # Generate target filename
            target_filename = self.generate_standard_filename(session_type, session_date)
            
            # Determine target directory based on date
            if "2025-07" in session_date or "2025_07" in session_date:
                target_path = self.target_dir / "2025_07" / target_filename
            elif "2025-08" in session_date or "2025_08" in session_date:
                target_path = self.target_dir / "2025_08" / target_filename
            else:
                target_path = self.target_dir / target_filename
            
            # Create backup of original file
            original_path = Path(file_info["file_path"])
            backup_path = self.backup_dir / original_path.name
            shutil.copy2(original_path, backup_path)
            
            # Write standardized file
            with open(target_path, 'w') as f:
                json.dump(standardized_data, f, indent=2)
            
            # Update statistics
            self.migration_stats["files_by_date"][session_date].append(target_filename)
            self.migration_stats["files_by_session_type"][session_type] += 1
            
            print(f"   ✅ Migrated: {original_path.name} → {target_path}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to migrate {file_info['file_name']}: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.migration_stats["quality_issues"].append(error_msg)
            return False

    def execute_full_migration(self) -> Dict[str, Any]:
        """Execute complete migration process."""
        print("🚀 Starting Level-1 Mass Migration")
        print("=" * 60)
        
        # Phase 1: Discovery and Analysis
        print("\n📊 Phase 1: Discovery and Analysis")
        analysis_results = self.analyzer.analyze_all_files()
        self.migration_stats["total_files_discovered"] = analysis_results["total_files"]
        
        # Phase 2: Create Directory Structure
        print("\n📁 Phase 2: Directory Structure Creation")
        self.create_directory_structure()
        
        # Phase 3: Collect all valid files for standardization
        print("\n🔧 Phase 3: File Standardization")
        
        all_files = []
        
        # Add current schema files
        for file_info in analysis_results.get("current_schema", []):
            all_files.append(Path(file_info["file_path"]))
        
        # Add legacy schema files
        for file_info in analysis_results.get("legacy_schema", []):
            all_files.append(Path(file_info["file_path"]))
        
        # Add partial schema files
        for file_info in analysis_results.get("partial_schema", []):
            all_files.append(Path(file_info["file_path"]))
        
        # Skip corrupted files for now
        corrupted_files = analysis_results.get("corrupted", []) + analysis_results.get("error", [])
        if corrupted_files:
            print(f"⚠️  Skipping {len(corrupted_files)} corrupted files:")
            for file_info in corrupted_files:
                print(f"   - {file_info['file_name']}: {file_info.get('error', 'Unknown error')}")
        
        # Standardize all valid files
        standardization_results = self.standardizer.standardize_all_files(all_files)
        
        # Phase 4: Group files by session type and date for deduplication
        print("\n🔍 Phase 4: Deduplication")
        
        files_by_key = defaultdict(list)
        
        for result in standardization_results["successful_conversions"]:
            standardized_data = result["standardized_data"]
            session_meta = standardized_data.get("session_metadata", {})
            session_type = session_meta.get("session_type", "unknown")
            session_date = session_meta.get("session_date", "unknown")
            
            key = f"{session_type}_{session_date}"
            
            # Add file info for deduplication
            file_info = {
                "file_name": Path(result["original_file"]).name,
                "file_path": result["original_file"],
                "standardized_data": standardized_data
            }
            files_by_key[key].append(file_info)
        
        # Resolve duplicates
        resolved_files = self.resolve_duplicates(files_by_key)
        
        # Phase 5: Migration
        print("\n📦 Phase 5: File Migration")
        
        successful_migrations = 0
        failed_migrations = 0
        
        for key, file_info in resolved_files.items():
            if self.migrate_file(file_info, file_info["standardized_data"]):
                successful_migrations += 1
            else:
                failed_migrations += 1
        
        self.migration_stats["successfully_migrated"] = successful_migrations
        self.migration_stats["failed_migrations"] = failed_migrations
        
        # Phase 6: Generate Migration Report
        print("\n📝 Phase 6: Migration Report Generation")
        report = self.generate_migration_report()
        
        with open("migration_report.json", 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📊 Migration report saved to: migration_report.json")
        
        return report

    def generate_migration_report(self) -> Dict[str, Any]:
        """Generate comprehensive migration report."""
        return {
            "migration_timestamp": datetime.now().isoformat(),
            "summary": {
                "total_files_found": self.migration_stats["total_files_discovered"],
                "successfully_migrated": self.migration_stats["successfully_migrated"],
                "failed_migrations": self.migration_stats["failed_migrations"],
                "duplicates_resolved": self.migration_stats["duplicates_resolved"]
            },
            "standardization_stats": self.standardizer.standardization_stats,
            "files_by_date": dict(self.migration_stats["files_by_date"]),
            "files_by_session_type": dict(self.migration_stats["files_by_session_type"]),
            "quality_issues": self.migration_stats["quality_issues"],
            "target_directory": str(self.target_dir),
            "backup_directory": str(self.backup_dir)
        }

if __name__ == "__main__":
    print("🎯 Level-1 Mass Migration System")
    print("=" * 50)
    
    migrator = Lvl1MassMigration()
    results = migrator.execute_full_migration()
    
    print(f"\n✅ MIGRATION COMPLETE!")
    print(f"📊 Successfully migrated: {results['summary']['successfully_migrated']} files")
    print(f"❌ Failed migrations: {results['summary']['failed_migrations']} files")
    print(f"🔄 Duplicates resolved: {results['summary']['duplicates_resolved']} files")
    print(f"📁 Target directory: {results['target_directory']}")
    print(f"💾 Backup directory: {results['backup_directory']}")
