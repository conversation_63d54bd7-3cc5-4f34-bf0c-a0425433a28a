"""Enhanced Multi-Dimensional Hawkes Process Engine

INHERITANCE-BASED ENHANCEMENT APPROACH:
- Inherits from proven grok-claude-automation HawkesCascadePredictor (91.1% accuracy)
- Adds multi-dimensional capabilities as enhancement layer
- Preserves ALL existing functionality and domain knowledge
- Implements Gemini's research discoveries: 97.16% MAE reduction, 28.32 min optimization

Mathematical Foundation:
- Base System: Proven HTF coupling with energy conservation (70% carryover)
- Enhancement: Multi-dimensional intensity λ(t) = λ_base(t) + Σ α_i * exp(-β_i * (t - t_j))
- VQE Integration: COBYLA optimization for 20+ parameter spaces
- Domain Constraints: HTF baseline 0.5, activation range 5.8x-883x

CRITICAL: This is an ENHANCEMENT, not a replacement. All proven logic is preserved.
"""

import numpy as np
import json
import math
from decimal import Decimal, getcontext
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
from scipy.optimize import minimize
import logging

# Import constraints and existing system components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from .constraints import (
        HTFConstants, SystemConstants, RGConstants, 
        BusinessRules, ValidationRules, CASCADE_TYPES_V1
    )
except ImportError:
    # Fallback for direct execution
    from constraints import (
        HTFConstants, SystemConstants, RGConstants, 
        BusinessRules, ValidationRules, CASCADE_TYPES_V1
    )

# Import cascade classification system
try:
    from .cascade_classifier import CascadeClassificationSystem, CascadeEvent, CascadeType
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from cascade_classifier import CascadeClassificationSystem, CascadeEvent, CascadeType

# Set high precision for calculations
getcontext().prec = 50

@dataclass
class MultiDimParameters:
    """Multi-dimensional Hawkes parameters for enhanced prediction"""
    dimensions: int
    alphas: List[Decimal]  # Excitation coefficients for each dimension
    decays: List[Decimal]  # Decay rates for each dimension
    cross_excitation_matrix: Optional[np.ndarray] = None
    optimization_method: str = "COBYLA"
    last_optimized: Optional[datetime] = None

@dataclass
class EnhancedPrediction:
    """Enhanced prediction result with multi-dimensional analysis"""
    base_prediction: Dict[str, Any]
    multi_dim_enhancement: Dict[str, Any]
    combined_result: Dict[str, Any]
    enhancement_active: bool
    confidence_boost: float
    cascade_analysis: Optional[Dict[str, Any]] = None  # NEW: Cascade analysis results

class EnhancedHawkesEngine:
    """
    Enhanced Hawkes Engine with Multi-Dimensional Capabilities
    
    INHERITANCE PATTERN: Wraps and enhances existing HawkesCascadePredictor
    - Preserves ALL existing 91.1% accuracy foundation
    - Adds Gemini's multi-dimensional enhancement layer
    - Maintains domain constraints and business rules
    - Provides seamless fallback to base system
    """
    
    def __init__(self, enable_enhancement: bool = True, config: Optional[Dict] = None):
        """
        Initialize Enhanced Hawkes Engine
        
        Args:
            enable_enhancement: Enable multi-dimensional enhancement layer
            config: Optional configuration overrides
        """
        
        # Configuration
        self.config = config or {}
        self.enable_enhancement = enable_enhancement
        self.log_level = self.config.get('log_level', 'INFO')
        
        # Set up logging
        logging.basicConfig(level=getattr(logging, self.log_level))
        self.logger = logging.getLogger(__name__)
        
        # CRITICAL: Try to import and use existing base system
        self.base_predictor = None
        self.base_available = False
        
        try:
            # Attempt to import existing Hawkes system
            from hawkes_cascade_predictor import HawkesCascadePredictor
            self.base_predictor = HawkesCascadePredictor()
            self.base_available = True
            self.logger.info("✅ Base HawkesCascadePredictor loaded successfully")
        except ImportError as e:
            self.logger.warning(f"⚠️ Base system not available: {e}")
            self.logger.info("🔧 Operating in standalone enhancement mode")
            self.base_available = False
        
        # Multi-dimensional enhancement parameters
        self.multi_dim_params = None
        self.enhancement_active = False
        
        # Cascade classification system
        self.cascade_classifier = CascadeClassificationSystem()
        self.cascade_analysis_enabled = self.config.get('enable_cascade_analysis', True)
        
        # Load mathematical constants (IMMUTABLE)
        self.htf_parameters = {
            'mu_h': HTFConstants.MU_H,
            'alpha_h': HTFConstants.ALPHA_H,
            'beta_h': HTFConstants.BETA_H,
            'threshold_h': HTFConstants.THRESHOLD_H
        }
        
        # Energy conservation constants
        self.energy_carryover_rate = SystemConstants.ENERGY_CARRYOVER_RATE
        self.energy_thresholds = SystemConstants.ENERGY_THRESHOLDS
        
        self.logger.info("🚀 ENHANCED HAWKES ENGINE: Initialized")
        self.logger.info(f"   Base System Available: {self.base_available}")
        self.logger.info(f"   Enhancement Layer: {'Enabled' if enable_enhancement else 'Disabled'}")
        self.logger.info(f"   HTF Baseline: {self.htf_parameters['threshold_h']}")
        
        # Validate initialization
        self._validate_initialization()
    
    def _validate_initialization(self) -> None:
        """Validate system initialization with domain constraints"""
        
        # Validate HTF parameters are within operating bounds
        htf_range_min = self.htf_parameters['threshold_h'] * HTFConstants.MIN_ACTIVATION_MULTIPLIER
        htf_range_max = self.htf_parameters['threshold_h'] * HTFConstants.MAX_ACTIVATION_MULTIPLIER
        
        # Adjust bounds to match actual HTF operating range (2.9 to 441.5)
        if not (2.0 <= float(htf_range_min) <= 10.0):
            self.logger.warning(f"HTF minimum activation: {htf_range_min} (expected range 2.9-10.0)")
        
        if not (400.0 <= float(htf_range_max) <= 500.0):
            self.logger.warning(f"HTF maximum activation: {htf_range_max} (expected range 400-500)")
        
        # Validate energy conservation law
        if not ValidationRules.validate_theory_weights():
            raise ValueError("Theory weights validation failed")
        
        self.logger.info("✅ Enhanced Hawkes Engine validation passed")
    
    def initialize_multi_dimensional_parameters(self, dimensions: int = 10, 
                                               historical_data: Optional[List[Dict]] = None) -> MultiDimParameters:
        """
        Initialize multi-dimensional Hawkes parameters
        
        Args:
            dimensions: Number of dimensions for multi-scale analysis
            historical_data: Historical data for parameter optimization
            
        Returns:
            MultiDimParameters: Initialized parameters
        """
        
        if dimensions > 20:
            self.logger.warning(f"Large dimension count ({dimensions}) may impact performance")
        
        # Initialize alphas and decays with domain-informed defaults
        alphas = []
        decays = []
        
        for i in range(dimensions):
            # Scale parameters based on HTF system
            alpha_base = float(self.htf_parameters['alpha_h']) / dimensions  # Distribute excitation
            decay_base = float(self.htf_parameters['beta_h']) * (1 + 0.1 * i)  # Vary decay rates
            
            alphas.append(Decimal(str(alpha_base)))
            decays.append(Decimal(str(decay_base)))
        
        # Create cross-excitation matrix (initially identity-based)
        cross_matrix = np.eye(dimensions) * 0.1  # Small cross-excitation
        
        params = MultiDimParameters(
            dimensions=dimensions,
            alphas=alphas,
            decays=decays,
            cross_excitation_matrix=cross_matrix,
            optimization_method="COBYLA",
            last_optimized=datetime.now()
        )
        
        self.multi_dim_params = params
        
        self.logger.info(f"🔧 Multi-dimensional parameters initialized:")
        self.logger.info(f"   Dimensions: {dimensions}")
        self.logger.info(f"   Alpha range: [{min(alphas):.4f}, {max(alphas):.4f}]")
        self.logger.info(f"   Decay range: [{min(decays):.4f}, {max(decays):.4f}]")
        
        return params
    
    def calculate_multi_dimensional_intensity(self, t: float, events: List[Dict], 
                                            params: MultiDimParameters) -> Dict[str, Any]:
        """
        Calculate multi-dimensional Hawkes intensity
        Implementation of Gemini's validated formula from real_world_vqe_optimization.py
        
        Args:
            t: Current time point
            events: Historical events
            params: Multi-dimensional parameters
            
        Returns:
            Dict containing intensity calculation results
        """
        
        if not events:
            return {
                'total_intensity': float(self.htf_parameters['mu_h']),
                'base_intensity': float(self.htf_parameters['mu_h']),
                'dimensional_contributions': [0.0] * params.dimensions,
                'enhancement_factor': 0.0
            }
        
        # Start with HTF baseline intensity (preserve domain knowledge)
        base_intensity = float(self.htf_parameters['mu_h'])
        
        # Calculate dimensional contributions
        dimensional_contributions = []
        total_enhancement = 0.0
        
        for dim in range(params.dimensions):
            alpha = float(params.alphas[dim])
            decay = float(params.decays[dim])
            
            # Sum excitation from past events for this dimension
            dimension_intensity = 0.0
            
            for event in events:
                event_time = event.get('time_minutes', 0.0)
                if event_time < t:
                    time_diff = t - event_time
                    
                    # Apply Gemini's validated formula
                    excitation = alpha * np.exp(-decay * time_diff)
                    dimension_intensity += excitation
            
            dimensional_contributions.append(dimension_intensity)
            total_enhancement += dimension_intensity
        
        # Calculate total intensity with enhancement
        total_intensity = base_intensity + total_enhancement
        
        # Apply HTF activation bounds (domain constraint)
        htf_min = float(self.htf_parameters['threshold_h'] * HTFConstants.MIN_ACTIVATION_MULTIPLIER)
        htf_max = float(self.htf_parameters['threshold_h'] * HTFConstants.MAX_ACTIVATION_MULTIPLIER)
        
        # Soft bounds to prevent system instability
        if total_intensity > htf_max:
            total_intensity = htf_max + np.log(1 + total_intensity - htf_max)
            self.logger.debug(f"Applied HTF upper bound: {total_intensity:.3f}")
        
        return {
            'total_intensity': total_intensity,
            'base_intensity': base_intensity,
            'dimensional_contributions': dimensional_contributions,
            'enhancement_factor': total_enhancement / base_intensity if base_intensity > 0 else 0.0,
            'htf_bounds_applied': total_intensity != (base_intensity + total_enhancement)
        }
    
    def optimize_parameters_vqe(self, historical_events: List[Dict], 
                               target_accuracy: float = 0.95) -> MultiDimParameters:
        """
        Optimize multi-dimensional parameters using VQE-inspired approach
        Implementation of Gemini's COBYLA optimization (28.32 min MAE)
        
        Args:
            historical_events: Historical event data for optimization
            target_accuracy: Target prediction accuracy
            
        Returns:
            Optimized MultiDimParameters
        """
        
        if not self.multi_dim_params:
            self.initialize_multi_dimensional_parameters()
        
        self.logger.info("🔬 VQE Parameter Optimization Starting")
        
        # Prepare optimization data
        event_times = []
        for event in historical_events:
            time_val = event.get('time_minutes', event.get('timestamp', 0))
            if isinstance(time_val, str):
                # Parse timestamp like "10:30"
                try:
                    parts = time_val.split(':')
                    time_val = int(parts[0]) * 60 + int(parts[1])
                except:
                    time_val = 0
            event_times.append(float(time_val))
        
        event_times = np.array(sorted(event_times))
        
        if len(event_times) < 2:
            self.logger.warning("Insufficient event data for optimization")
            return self.multi_dim_params
        
        # Define VQE objective function (Gemini's validated approach)
        def vqe_objective(params_array):
            """VQE objective function for parameter optimization"""
            
            num_dims = len(params_array) // 2
            alphas = [Decimal(str(p)) for p in params_array[0::2]]
            decays = [Decimal(str(p)) for p in params_array[1::2]]
            
            # Constraint violations
            if any(d <= 0 for d in params_array[1::2]) or any(a < 0 for a in params_array[0::2]):
                return np.inf
            
            # Create temporary parameters
            temp_params = MultiDimParameters(
                dimensions=num_dims,
                alphas=alphas,
                decays=decays
            )
            
            # Calculate predictions vs actual
            predictions = []
            for i in range(1, len(event_times)):
                history_events = [{'time_minutes': t} for t in event_times[:i]]
                
                intensity_result = self.calculate_multi_dimensional_intensity(
                    event_times[i-1], history_events, temp_params
                )
                
                # Predict next event time based on intensity
                intensity = intensity_result['total_intensity']
                if intensity > 1e-6:
                    dt_prediction = 1.0 / intensity
                    predicted_time = event_times[i-1] + dt_prediction
                else:
                    predicted_time = event_times[i-1] + 60.0  # Default 1 hour
                
                predictions.append(predicted_time)
            
            # Calculate Mean Absolute Error
            actual_next_times = event_times[1:]
            if len(predictions) != len(actual_next_times):
                return np.inf
            
            mae = np.mean(np.abs(np.array(predictions) - actual_next_times))
            
            return mae
        
        # Initialize parameters for optimization
        initial_params = []
        for i in range(self.multi_dim_params.dimensions):
            initial_params.extend([
                float(self.multi_dim_params.alphas[i]),
                float(self.multi_dim_params.decays[i])
            ])
        
        self.logger.info(f"   Optimizing {len(initial_params)} parameters")
        self.logger.info(f"   Using {len(event_times)} historical events")
        
        # Run COBYLA optimization (Gemini's validated method)
        try:
            result = minimize(
                vqe_objective,
                initial_params,
                method='COBYLA',
                options={
                    'maxiter': 1000,  # Limit iterations for performance
                    'rhobeg': 0.5,
                    'disp': False
                }
            )
            
            if result.success or result.fun < np.inf:
                # Update parameters with optimized values
                optimized_alphas = [Decimal(str(p)) for p in result.x[0::2]]
                optimized_decays = [Decimal(str(p)) for p in result.x[1::2]]
                
                self.multi_dim_params.alphas = optimized_alphas
                self.multi_dim_params.decays = optimized_decays
                self.multi_dim_params.last_optimized = datetime.now()
                
                self.logger.info(f"✅ VQE Optimization completed:")
                self.logger.info(f"   Final MAE: {result.fun:.4f} minutes")
                self.logger.info(f"   Iterations: {result.nit if hasattr(result, 'nit') else 'N/A'}")
                self.logger.info(f"   Success: {result.success}")
                
            else:
                self.logger.warning(f"⚠️ VQE Optimization failed: {result.message}")
                
        except Exception as e:
            self.logger.error(f"❌ VQE Optimization error: {e}")
        
        return self.multi_dim_params
    
    def predict_cascade_timing(self, session_data: Dict[str, Any], 
                              enable_multi_dim: bool = None) -> EnhancedPrediction:
        """
        Enhanced cascade timing prediction with multi-dimensional analysis
        
        INHERITANCE PATTERN:
        1. Use base system prediction (if available)
        2. Add multi-dimensional enhancement layer
        3. Combine results with confidence weighting
        4. Preserve ALL domain constraints
        
        Args:
            session_data: Session data for prediction
            enable_multi_dim: Override enhancement enable setting
            
        Returns:
            EnhancedPrediction with base + enhanced results
        """
        
        enhancement_enabled = (
            enable_multi_dim if enable_multi_dim is not None 
            else self.enable_enhancement
        )
        
        self.logger.info("🎯 ENHANCED CASCADE PREDICTION")
        self.logger.info(f"   Enhancement Layer: {'Enabled' if enhancement_enabled else 'Disabled'}")
        
        # STEP 1: Base system prediction (preserve 91.1% accuracy foundation)
        base_prediction = None
        base_available = self.base_available and self.base_predictor is not None
        
        if base_available:
            try:
                base_result = self.base_predictor.predict_cascade_timing(session_data)
                base_prediction = {
                    'predicted_time': base_result.predicted_cascade_time,
                    'confidence': base_result.prediction_confidence,
                    'methodology': base_result.methodology,
                    'parameters': {
                        'mu': base_result.parameters_used.mu,
                        'alpha': base_result.parameters_used.alpha,
                        'beta': base_result.parameters_used.beta,
                        'threshold': base_result.parameters_used.threshold
                    }
                }
                self.logger.info(f"✅ Base prediction: {base_result.predicted_cascade_time:.1f} min")
            except Exception as e:
                self.logger.error(f"❌ Base prediction failed: {e}")
                base_available = False
        
        # STEP 2: Multi-dimensional enhancement (if enabled)
        multi_dim_prediction = None
        enhancement_active = False
        
        if enhancement_enabled and self.multi_dim_params:
            try:
                # Extract events from session data
                events = self._extract_events_from_session(session_data)
                
                # NEW: Cascade Analysis
                cascade_analysis = None
                if self.cascade_analysis_enabled and events:
                    cascade_analysis = self._analyze_cascade_events(events, session_data)
                
                if events:
                    # Calculate enhanced intensity over time
                    session_duration = session_data.get('session_metadata', {}).get('duration_minutes', 150)
                    max_intensity = 0.0
                    optimal_time = session_duration * 0.4  # Default to mid-session
                    
                    # Get the time range of actual events
                    event_times = [e['time_minutes'] for e in events]
                    min_event_time = min(event_times) if event_times else 0
                    max_event_time = max(event_times) if event_times else session_duration
                    
                    # Analyze intensity buildup starting after first event
                    analysis_start = max(5, min_event_time)
                    analysis_end = min(session_duration, max_event_time + 30, 120)
                    
                    for t in range(int(analysis_start), int(analysis_end), 5):  # 5-minute intervals
                        intensity_result = self.calculate_multi_dimensional_intensity(
                            t, events, self.multi_dim_params
                        )
                        
                        if intensity_result['total_intensity'] > max_intensity:
                            max_intensity = intensity_result['total_intensity']
                            optimal_time = t
                        
                        # Early termination if strong signal
                        if intensity_result['total_intensity'] > float(self.htf_parameters['threshold_h']) * 2:
                            optimal_time = t
                            break
                    
                    # Ensure optimal_time is reasonable
                    if optimal_time <= 0:
                        optimal_time = min(30.0, session_duration * 0.25)  # Quarter session fallback
                    
                    # Calculate enhancement confidence
                    htf_threshold = float(self.htf_parameters['threshold_h'])
                    enhancement_confidence = min(0.95, max_intensity / htf_threshold - 0.1)
                    enhancement_confidence = max(0.1, enhancement_confidence)
                    
                    multi_dim_prediction = {
                        'predicted_time': optimal_time,
                        'confidence': enhancement_confidence,
                        'max_intensity': max_intensity,
                        'htf_threshold': htf_threshold,
                        'dimensions_used': self.multi_dim_params.dimensions
                    }
                    
                    enhancement_active = True
                    self.logger.info(f"✅ Multi-dim prediction: {optimal_time:.1f} min")
                
            except Exception as e:
                self.logger.error(f"❌ Multi-dimensional enhancement failed: {e}")
        
        # STEP 3: Combine predictions (weighted approach)
        combined_result = None
        confidence_boost = 0.0
        
        if base_available and enhancement_active:
            # Weighted combination of base and enhanced predictions
            base_weight = 0.6  # Preserve base system authority
            enhanced_weight = 0.4
            
            combined_time = (
                base_weight * base_prediction['predicted_time'] +
                enhanced_weight * multi_dim_prediction['predicted_time']
            )
            
            combined_confidence = (
                base_weight * base_prediction['confidence'] +
                enhanced_weight * multi_dim_prediction['confidence']
            )
            
            confidence_boost = multi_dim_prediction['confidence'] * 0.2  # Up to 20% boost
            
            combined_result = {
                'predicted_time': combined_time,
                'confidence': min(0.95, combined_confidence + confidence_boost),
                'methodology': 'enhanced_multi_dimensional_hawkes',
                'base_weight': base_weight,
                'enhancement_weight': enhanced_weight
            }
            
            self.logger.info(f"🔀 Combined prediction: {combined_time:.1f} min")
            
        elif base_available:
            # Use base prediction only
            combined_result = base_prediction.copy()
            combined_result['methodology'] = 'base_hawkes_preserved'
            
        elif enhancement_active:
            # Use enhanced prediction only
            combined_result = multi_dim_prediction.copy()
            combined_result['methodology'] = 'multi_dimensional_hawkes_only'
            
        else:
            # Fallback to session-based heuristic calculation
            session_duration = session_data.get('session_metadata', {}).get('duration_minutes', 150)
            session_range = session_data.get('price_data', {}).get('range', 100)
            session_character = session_data.get('price_data', {}).get('session_character', '')
            
            # Heuristic prediction based on session characteristics
            if 'expansion' in session_character.lower():
                predicted_time = min(30.0, session_duration * 0.2)  # Early expansion
            elif 'consolidation' in session_character.lower():
                predicted_time = min(90.0, session_duration * 0.6)  # Late consolidation
            else:
                predicted_time = min(60.0, session_duration * 0.4)  # Mid-session
                
            # Add volatility adjustment
            volatility_factor = min(2.0, session_range / 100.0)
            predicted_time = predicted_time / max(0.5, volatility_factor)
            
            combined_result = {
                'predicted_time': predicted_time,
                'confidence': 0.3,  # Low confidence for fallback
                'methodology': 'fallback_heuristic'
            }
            self.logger.warning(f"⚠️ Using fallback prediction: {predicted_time:.1f} min")
        
        # Validate against domain constraints
        self._validate_prediction_constraints(combined_result)
        
        return EnhancedPrediction(
            base_prediction=base_prediction or {},
            multi_dim_enhancement=multi_dim_prediction or {},
            combined_result=combined_result,
            enhancement_active=enhancement_active,
            confidence_boost=confidence_boost,
            cascade_analysis=cascade_analysis  # NEW: Include cascade analysis
        )
    
    def _extract_events_from_session(self, session_data: Dict[str, Any]) -> List[Dict]:
        """Extract events from session data for multi-dimensional analysis"""
        
        events = []
        
        # Try multiple data sources
        sources = [
            session_data.get('micro_timing_analysis', {}).get('cascade_events', []),
            session_data.get('events', []),
            session_data.get('price_movements', [])
        ]
        
        for source in sources:
            if source:
                for event in source:
                    processed_event = {
                        'time_minutes': self._parse_time_to_minutes(event.get('timestamp', '00:00')),
                        'price_level': event.get('price_level', event.get('price', 0)),
                        'event_type': event.get('event_type', 'unknown')
                    }
                    events.append(processed_event)
        
        # Sort by time
        events.sort(key=lambda e: e['time_minutes'])
        
        return events
    
    def _parse_time_to_minutes(self, timestamp: str) -> float:
        """Parse timestamp to minutes from start of day"""
        
        try:
            if ':' in timestamp:
                parts = timestamp.split(':')
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = int(parts[2]) if len(parts) > 2 else 0
                return hours * 60 + minutes + seconds / 60.0
            else:
                return float(timestamp)
        except:
            return 0.0
    
    def _validate_prediction_constraints(self, prediction: Dict[str, Any]) -> None:
        """Validate prediction against domain constraints"""
        
        predicted_time = prediction.get('predicted_time', 0)
        
        # Validate time bounds
        if predicted_time < 0:
            prediction['predicted_time'] = 0
            self.logger.warning("Corrected negative prediction time")
        
        if predicted_time > 480:  # 8 hours max
            prediction['predicted_time'] = 480
            self.logger.warning("Corrected excessive prediction time")
        
        # Validate confidence bounds
        confidence = prediction.get('confidence', 0)
        prediction['confidence'] = max(0.0, min(1.0, confidence))
    
    def validate_enhanced_performance(self, test_cases: List[Dict], 
                                    baseline_metrics: Dict[str, float]) -> Dict[str, Any]:
        """
        Validate enhanced system performance against baseline
        CRITICAL: Enhanced system must not degrade baseline performance
        
        Args:
            test_cases: Test cases with known ground truth
            baseline_metrics: Baseline performance metrics to maintain
            
        Returns:
            Performance comparison results
        """
        
        self.logger.info("📊 ENHANCED SYSTEM VALIDATION")
        
        enhanced_errors = []
        enhanced_confidences = []
        
        for test_case in test_cases:
            session_data = test_case.get('session_data', {})
            actual_time = test_case.get('actual_cascade_time', 0)
            
            # Generate enhanced prediction
            enhanced_prediction = self.predict_cascade_timing(session_data)
            predicted_time = enhanced_prediction.combined_result['predicted_time']
            confidence = enhanced_prediction.combined_result['confidence']
            
            error = abs(predicted_time - actual_time)
            enhanced_errors.append(error)
            enhanced_confidences.append(confidence)
        
        # Calculate enhanced metrics
        enhanced_metrics = {
            'mean_absolute_error': np.mean(enhanced_errors),
            'median_absolute_error': np.median(enhanced_errors),
            'max_absolute_error': np.max(enhanced_errors),
            'mean_confidence': np.mean(enhanced_confidences),
            'success_rate': sum(1 for e in enhanced_errors if e <= 10.0) / len(enhanced_errors)
        }
        
        # Performance comparison
        baseline_mae = baseline_metrics.get('mean_absolute_error', 30.0)
        baseline_success = baseline_metrics.get('success_rate', 0.5)
        
        performance_maintained = (
            enhanced_metrics['mean_absolute_error'] <= baseline_mae * 1.1 and  # 10% tolerance
            enhanced_metrics['success_rate'] >= baseline_success * 0.9         # 10% tolerance
        )
        
        validation_result = {
            'enhanced_metrics': enhanced_metrics,
            'baseline_metrics': baseline_metrics,
            'performance_maintained': performance_maintained,
            'improvement_mae': (baseline_mae - enhanced_metrics['mean_absolute_error']) / baseline_mae,
            'improvement_success': enhanced_metrics['success_rate'] - baseline_success,
            'validation_passed': performance_maintained
        }
        
        if performance_maintained:
            self.logger.info("✅ Enhanced system maintains baseline performance")
        else:
            self.logger.error("❌ Enhanced system degrades baseline performance")
            self.logger.error("🚨 MIGRATION VALIDATION FAILED")
        
        return validation_result
    
    def _analyze_cascade_events(self, events: List[Dict], session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze cascade events using the cascade classification system"""
        
        if not events:
            return {'no_events': True}
        
        try:
            # Convert events to cascade events for analysis
            cascade_events = []
            for event in events:
                # Create event data for classifier
                event_data = {
                    'timestamp': event.get('timestamp', '00:00:00'),
                    'price_level': event.get('price_level', 0.0),
                    'movement_type': event.get('event_type', ''),
                    'event_context': event.get('event_type', '')
                }
                
                cascade_event = self.cascade_classifier.classify_cascade_event(event_data)
                cascade_events.append(cascade_event)
            
            # Detect cascade sequences
            sequences = self.cascade_classifier.detect_cascade_sequences(cascade_events)
            
            # Analyze sequence patterns
            sequence_analysis = None
            if sequences:
                sequence_analysis = self.cascade_classifier.sequence_analyzer.analyze_sequence_pattern(
                    [event for seq in sequences for event in seq.events]
                )
            
            # Extract cascade type distribution
            cascade_types = [event.cascade_type.value for event in cascade_events]
            type_distribution = {}
            for cascade_type in cascade_types:
                type_distribution[cascade_type] = type_distribution.get(cascade_type, 0) + 1
            
            analysis_result = {
                'total_events': len(cascade_events),
                'cascade_types_detected': list(set(cascade_types)),
                'type_distribution': type_distribution,
                'sequences_detected': len(sequences),
                'sequence_patterns': [seq.sequence_type for seq in sequences] if sequences else [],
                'classified_events': [
                    {
                        'timestamp': event.timestamp,
                        'cascade_type': event.cascade_type.value,
                        'magnitude': event.magnitude,
                        'context': event.event_context
                    }
                    for event in cascade_events
                ]
            }
            
            # Add sequence analysis if available
            if sequence_analysis:
                analysis_result['sequence_analysis'] = sequence_analysis
            
            # Add dominant cascade type
            if type_distribution:
                dominant_type = max(type_distribution, key=type_distribution.get)
                analysis_result['dominant_cascade_type'] = dominant_type
                analysis_result['dominant_type_percentage'] = type_distribution[dominant_type] / len(cascade_events)
            
            self.logger.debug(f"🔍 Cascade Analysis: {len(cascade_events)} events, {len(sequences)} sequences")
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"❌ Cascade analysis failed: {e}")
            return {'analysis_error': str(e)}


def create_enhanced_hawkes_engine(config: Optional[Dict] = None) -> EnhancedHawkesEngine:
    """
    Factory function to create production-ready enhanced Hawkes engine
    
    Args:
        config: Optional configuration overrides
        
    Returns:
        EnhancedHawkesEngine: Configured instance ready for use
    """
    
    production_config = {
        'enable_enhancement': True,
        'log_level': 'INFO',
        'validate_domain_constraints': True,
        'preserve_base_system': True
    }
    
    if config:
        production_config.update(config)
    
    return EnhancedHawkesEngine(
        enable_enhancement=production_config['enable_enhancement'],
        config=production_config
    )


if __name__ == "__main__":
    """
    Test the Enhanced Hawkes Engine with inheritance pattern validation
    """
    
    print("🚀 ENHANCED HAWKES ENGINE: Testing & Validation")
    print("=" * 60)
    
    # Create enhanced engine
    engine = create_enhanced_hawkes_engine({'log_level': 'DEBUG'})
    
    # Initialize multi-dimensional parameters
    params = engine.initialize_multi_dimensional_parameters(dimensions=8)
    
    # Test prediction with sample data
    sample_session = {
        'session_metadata': {'duration_minutes': 120},
        'micro_timing_analysis': {
            'cascade_events': [
                {'timestamp': '09:30', 'price_level': 23500, 'event_type': 'open'},
                {'timestamp': '09:45', 'price_level': 23520, 'event_type': 'high'},
                {'timestamp': '10:00', 'price_level': 23480, 'event_type': 'low'},
                {'timestamp': '10:15', 'price_level': 23510, 'event_type': 'close'}
            ]
        }
    }
    
    print("\n📊 Testing Enhanced Prediction:")
    
    # Generate prediction
    prediction = engine.predict_cascade_timing(sample_session)
    
    print(f"   Base Available: {prediction.base_prediction != {}}")
    print(f"   Enhancement Active: {prediction.enhancement_active}")
    print(f"   Combined Prediction: {prediction.combined_result['predicted_time']:.1f} min")
    print(f"   Confidence: {prediction.combined_result['confidence']:.3f}")
    print(f"   Confidence Boost: {prediction.confidence_boost:.3f}")
    print(f"   Methodology: {prediction.combined_result['methodology']}")
    
    print("\n✅ Enhanced Hawkes Engine validation complete")
    print("🔗 Ready for integration with RG Scaler and VQE optimization")
