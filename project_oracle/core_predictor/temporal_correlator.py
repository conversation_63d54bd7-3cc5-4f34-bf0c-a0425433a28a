"""
Temporal Correlator - Extracted from cascade classifier for modular integration
Handles prediction-validation correlation and sequence analysis
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

@dataclass
class CorrelationResult:
    """Result from temporal correlation analysis"""
    prediction_time: str
    closest_event_time: Optional[str]
    time_error_minutes: float
    correlation_strength: float
    validation_status: str
    contextual_matches: List[Dict[str, Any]]

class TemporalCorrelationEngine:
    """Engine for correlating predictions with validation data across sequences"""
    
    def __init__(self):
        self.correlation_threshold = 0.7
        self.temporal_window_minutes = 15
        self.correlation_history = []
        
    def correlate_prediction_validation(self, prediction_time: str, actual_events: List[Any], 
                                      prediction_context: Optional[Dict] = None) -> CorrelationResult:
        """Correlate a prediction with actual cascade events"""
        
        correlation_result = CorrelationResult(
            prediction_time=prediction_time,
            closest_event_time=None,
            time_error_minutes=float('inf'),
            correlation_strength=0.0,
            validation_status='miss',
            contextual_matches=[]
        )
        
        if not actual_events:
            return correlation_result
        
        # Find closest event to prediction
        min_time_diff = float('inf')
        closest_event = None
        
        for event in actual_events:
            event_time = getattr(event, 'timestamp', None) or event.get('timestamp', '')
            if event_time:
                time_diff = self._calculate_time_difference(prediction_time, event_time)
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_event = event
        
        if closest_event and min_time_diff <= self.temporal_window_minutes:
            closest_time = getattr(closest_event, 'timestamp', None) or closest_event.get('timestamp', '')
            correlation_result.closest_event_time = closest_time
            correlation_result.time_error_minutes = min_time_diff
            correlation_result.correlation_strength = max(0, 1 - (min_time_diff / self.temporal_window_minutes))
            correlation_result.validation_status = self._determine_validation_status(min_time_diff)
            
            # Add contextual analysis if prediction context provided
            if prediction_context:
                correlation_result.contextual_matches = self._analyze_contextual_matches(
                    prediction_context, closest_event, actual_events
                )
        
        # Store correlation for learning
        self.correlation_history.append({
            'timestamp': datetime.now().isoformat(),
            'prediction_time': prediction_time,
            'error_minutes': correlation_result.time_error_minutes,
            'correlation_strength': correlation_result.correlation_strength,
            'validation_status': correlation_result.validation_status
        })
        
        return correlation_result
    
    def _determine_validation_status(self, time_error: float) -> str:
        """Determine validation status based on time error"""
        if time_error <= 2:
            return 'excellent'
        elif time_error <= 5:
            return 'good'
        elif time_error <= 10:
            return 'fair'
        elif time_error <= 15:
            return 'poor'
        else:
            return 'miss'
    
    def _calculate_time_difference(self, time1: str, time2: str) -> float:
        """Calculate absolute time difference in minutes"""
        try:
            # Handle various time formats
            if len(time1.split(':')) == 2:
                time1 += ':00'
            if len(time2.split(':')) == 2:
                time2 += ':00'
                
            dt1 = datetime.strptime(time1, '%H:%M:%S')
            dt2 = datetime.strptime(time2, '%H:%M:%S')
            
            diff = abs((dt2 - dt1).total_seconds() / 60)
            
            # Handle day rollover
            if diff > 12 * 60:  # More than 12 hours suggests day rollover
                diff = 24 * 60 - diff
                
            return diff
        except:
            return float('inf')
    
    def _analyze_contextual_matches(self, prediction_context: Dict, closest_event: Any, 
                                   all_events: List[Any]) -> List[Dict[str, Any]]:
        """Analyze contextual matches between prediction and actual events"""
        
        matches = []
        
        # Context type matching
        predicted_cascade_type = prediction_context.get('cascade_type', '')
        if hasattr(closest_event, 'cascade_type'):
            actual_cascade_type = closest_event.cascade_type.value
            if predicted_cascade_type == actual_cascade_type:
                matches.append({
                    'match_type': 'cascade_type',
                    'predicted': predicted_cascade_type,
                    'actual': actual_cascade_type,
                    'confidence': 1.0
                })
        
        # Magnitude matching
        if 'magnitude_range' in prediction_context and hasattr(closest_event, 'magnitude'):
            pred_min, pred_max = prediction_context['magnitude_range']
            actual_magnitude = closest_event.magnitude
            if pred_min <= actual_magnitude <= pred_max:
                matches.append({
                    'match_type': 'magnitude',
                    'predicted_range': (pred_min, pred_max),
                    'actual': actual_magnitude,
                    'confidence': 0.8
                })
        
        return matches
    
    def get_correlation_statistics(self) -> Dict[str, Any]:
        """Get statistics on correlation performance"""
        
        if not self.correlation_history:
            return {'no_data': True}
        
        recent_correlations = self.correlation_history[-50:]  # Last 50 correlations
        
        error_minutes = [c['error_minutes'] for c in recent_correlations if c['error_minutes'] != float('inf')]
        correlation_strengths = [c['correlation_strength'] for c in recent_correlations]
        
        status_counts = {}
        for c in recent_correlations:
            status = c['validation_status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            'total_correlations': len(recent_correlations),
            'avg_error_minutes': np.mean(error_minutes) if error_minutes else float('inf'),
            'avg_correlation_strength': np.mean(correlation_strengths) if correlation_strengths else 0,
            'validation_distribution': status_counts,
            'accuracy_rate': (status_counts.get('excellent', 0) + status_counts.get('good', 0)) / len(recent_correlations)
        }

class SequencePatternAnalyzer:
    """Analyzes cascade sequence patterns for predictive insights"""
    
    def __init__(self):
        self.known_patterns = {
            'primer_to_major': {
                'sequence': ['primer_cascade', 'standard_cascade', 'major_cascade'],
                'typical_duration_minutes': 45,
                'success_rate': 0.75
            },
            'escalation_sequence': {
                'sequence': ['primer_cascade', 'standard_cascade', 'major_cascade'],
                'typical_duration_minutes': 30,
                'success_rate': 0.82
            },
            'reversal_pattern': {
                'sequence': ['major_cascade', 'standard_cascade', 'primer_cascade'],
                'typical_duration_minutes': 60,
                'success_rate': 0.65
            }
        }
        
        self.pattern_history = []
    
    def analyze_sequence_pattern(self, cascade_events: List[Any]) -> Dict[str, Any]:
        """Analyze a sequence of cascade events for known patterns"""
        
        if len(cascade_events) < 2:
            return {'pattern_type': 'insufficient_data', 'confidence': 0.0}
        
        # Extract cascade type sequence
        event_types = []
        for event in cascade_events:
            if hasattr(event, 'cascade_type'):
                event_types.append(event.cascade_type.value)
            else:
                event_types.append('unknown')
        
        # Match against known patterns
        best_match = None
        best_confidence = 0.0
        
        for pattern_name, pattern_data in self.known_patterns.items():
            confidence = self._calculate_pattern_match(event_types, pattern_data['sequence'])
            if confidence > best_confidence:
                best_confidence = confidence
                best_match = pattern_name
        
        # Calculate sequence characteristics
        sequence_duration = self._calculate_sequence_duration(cascade_events)
        
        result = {
            'pattern_type': best_match or 'unknown_pattern',
            'confidence': best_confidence,
            'sequence_duration_minutes': sequence_duration,
            'event_count': len(cascade_events),
            'event_types': event_types
        }
        
        # Add predictions if pattern is recognized
        if best_match and best_confidence > 0.6:
            expected_pattern = self.known_patterns[best_match]
            result['predictions'] = self._generate_pattern_predictions(
                cascade_events, expected_pattern
            )
        
        return result
    
    def _calculate_pattern_match(self, actual_sequence: List[str], expected_pattern: List[str]) -> float:
        """Calculate how well an actual sequence matches an expected pattern"""
        
        if not actual_sequence or not expected_pattern:
            return 0.0
        
        # Exact match
        if actual_sequence == expected_pattern:
            return 1.0
        
        # Partial match - check for subsequences
        max_subsequence_length = 0
        for i in range(len(actual_sequence)):
            for j in range(i + 1, len(actual_sequence) + 1):
                subseq = actual_sequence[i:j]
                if self._is_subsequence_in_pattern(subseq, expected_pattern):
                    max_subsequence_length = max(max_subsequence_length, len(subseq))
        
        return max_subsequence_length / len(expected_pattern)
    
    def _is_subsequence_in_pattern(self, subseq: List[str], pattern: List[str]) -> bool:
        """Check if subsequence exists in pattern maintaining order"""
        if len(subseq) > len(pattern):
            return False
        
        for i in range(len(pattern) - len(subseq) + 1):
            if pattern[i:i+len(subseq)] == subseq:
                return True
        return False
    
    def _calculate_sequence_duration(self, cascade_events: List[Any]) -> float:
        """Calculate total duration of cascade sequence"""
        if len(cascade_events) < 2:
            return 0.0
        
        start_time = getattr(cascade_events[0], 'timestamp', '00:00:00')
        end_time = getattr(cascade_events[-1], 'timestamp', '00:00:00')
        
        try:
            start_dt = datetime.strptime(start_time, '%H:%M:%S')
            end_dt = datetime.strptime(end_time, '%H:%M:%S')
            return (end_dt - start_dt).total_seconds() / 60
        except:
            return 0.0
    
    def _generate_pattern_predictions(self, current_events: List[Any], 
                                     expected_pattern: Dict) -> Dict[str, Any]:
        """Generate predictions based on recognized pattern"""
        
        current_types = [getattr(e, 'cascade_type', 'unknown').value if hasattr(getattr(e, 'cascade_type', None), 'value') else 'unknown' 
                        for e in current_events]
        expected_sequence = expected_pattern['sequence']
        
        # Find where we are in the expected pattern
        pattern_position = len(current_types)
        
        predictions = {
            'next_cascade_types': [],
            'expected_completion_time': None,
            'pattern_confidence': expected_pattern['success_rate']
        }
        
        # Predict remaining cascade types
        if pattern_position < len(expected_sequence):
            predictions['next_cascade_types'] = expected_sequence[pattern_position:]
        
        # Estimate completion time
        if current_events:
            last_time = getattr(current_events[-1], 'timestamp', '00:00:00')
            try:
                last_dt = datetime.strptime(last_time, '%H:%M:%S')
                remaining_duration = expected_pattern['typical_duration_minutes'] * (1 - pattern_position / len(expected_sequence))
                completion_dt = last_dt + timedelta(minutes=remaining_duration)
                predictions['expected_completion_time'] = completion_dt.strftime('%H:%M:%S')
            except:
                pass
        
        return predictions